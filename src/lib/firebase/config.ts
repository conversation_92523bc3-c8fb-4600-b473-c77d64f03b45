import { initializeApp, getApps, FirebaseApp } from 'firebase/app'
import { getFirestore, Firestore, connectFirestoreEmulator, initializeFirestore, persistentLocalCache, memoryLocalCache } from 'firebase/firestore'
import { getAuth, Auth, connectAuthEmulator } from 'firebase/auth'

// Firebase configuration interface
export interface FirebaseConfig {
  apiKey: string
  authDomain: string
  projectId: string
  storageBucket: string
  messagingSenderId: string
  appId: string
  measurementId?: string
}

// Get Firebase configuration from environment variables
const firebaseConfig: FirebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || '',
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || undefined
}

// Validate Firebase configuration
const validateFirebaseConfig = (config: FirebaseConfig): boolean => {
  const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId']
  const isValid = requiredFields.every(field => {
    const value = config[field as keyof FirebaseConfig]
    return value && value.trim() !== '' && value !== 'your_firebase_api_key' && value !== 'your_project_id'
  })

  if (!isValid) {
    console.log('🔧 Firebase configuration incomplete or contains placeholder values')
    console.log('📝 Add your Firebase config to .env.local to enable authentication and cloud storage')
  }

  return isValid
}

// Initialize Firebase app
let app: FirebaseApp
let db: Firestore
let auth: Auth

export const initializeFirebase = (): { app: FirebaseApp; db: Firestore; auth: Auth } | null => {
  try {
    // Check if Firebase is already initialized
    if (getApps().length > 0) {
      app = getApps()[0]
    } else {
      // Validate configuration before initializing
      if (!validateFirebaseConfig(firebaseConfig)) {
        return null
      }

      // Initialize Firebase
      app = initializeApp(firebaseConfig)
      console.log('✅ Firebase app initialized')
    }

    // Initialize Firestore with offline persistence
    if (typeof window !== 'undefined') {
      // Client-side: Enable offline persistence
      try {
        db = initializeFirestore(app, {
          localCache: persistentLocalCache({
            // Configure cache size (100MB default, can be customized)
            cacheSizeBytes: 100 * 1024 * 1024
          })
        })
        console.log('✅ Firestore initialized with offline persistence')
      } catch (error) {
        console.warn('⚠️ Failed to initialize with persistence, falling back to memory cache:', error)
        // Fallback to memory cache if persistence fails
        db = initializeFirestore(app, {
          localCache: memoryLocalCache()
        })
      }
    } else {
      // Server-side: Use regular Firestore
      db = getFirestore(app)
    }
    
    // Initialize Auth
    auth = getAuth(app)

    // Connect to emulators in development (if needed)
    if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {
      try {
        connectFirestoreEmulator(db, 'localhost', 8080)
        connectAuthEmulator(auth, 'http://localhost:9099')
        console.log('🔧 Connected to Firebase emulators')
      } catch (error) {
        console.log('Firebase emulators already connected or not available')
      }
    }

    return { app, db, auth }
  } catch (error) {
    console.error('❌ Error initializing Firebase:', error)
    return null
  }
}

// Export Firebase instances
export const getFirebaseApp = (): FirebaseApp | null => app || null
export const getFirebaseDb = (): Firestore | null => db || null
export const getFirebaseAuth = (): Auth | null => auth || null

// Export configuration for external use
export { firebaseConfig }
